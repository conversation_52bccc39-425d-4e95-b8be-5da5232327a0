/**
 * Test Firebase initialization to verify the fix
 */

// Set up environment variables for testing
process.env.NEXT_PUBLIC_FIREBASE_API_KEY = "AIzaSyB6ALvnN6aX0DMVhePhkUow9VrPauBCqgQ";
process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN = "tap2go-kuucn.firebaseapp.com";
process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID = "tap2go-kuucn";
process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET = "tap2go-kuucn.firebasestorage.app";
process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID = "828629511294";
process.env.NEXT_PUBLIC_FIREBASE_APP_ID = "1:828629511294:web:fae32760ca3c3afcb87c2f";

// Mock window object for Node.js environment
global.window = {};

async function testFirebaseInit() {
  try {
    console.log('Testing Firebase initialization...');
    
    // Test firebase-config package
    const { getFirebaseDb, getFirebaseAuth } = require('firebase-config');
    
    console.log('✅ Firebase config package loaded successfully');
    
    // Test getting database instance
    try {
      const db = getFirebaseDb();
      console.log('✅ Firebase database initialized successfully');
      console.log('Database instance:', !!db);
    } catch (error) {
      console.log('❌ Firebase database initialization failed:', error.message);
    }
    
    // Test getting auth instance
    try {
      const auth = getFirebaseAuth();
      console.log('✅ Firebase auth initialized successfully');
      console.log('Auth instance:', !!auth);
    } catch (error) {
      console.log('❌ Firebase auth initialization failed:', error.message);
    }
    
    // Test shared-auth package
    try {
      const { UserDatabaseService } = require('shared-auth/services');
      const userDbService = new UserDatabaseService();
      console.log('✅ UserDatabaseService created successfully');
    } catch (error) {
      console.log('❌ UserDatabaseService creation failed:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testFirebaseInit();
