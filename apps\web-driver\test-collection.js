/**
 * Test Firebase collection() function to verify the fix
 */

// Set up environment variables for testing
process.env.NEXT_PUBLIC_FIREBASE_API_KEY = "AIzaSyB6ALvnN6aX0DMVhePhkUow9VrPauBCqgQ";
process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN = "tap2go-kuucn.firebaseapp.com";
process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID = "tap2go-kuucn";
process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET = "tap2go-kuucn.firebasestorage.app";
process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID = "828629511294";
process.env.NEXT_PUBLIC_FIREBASE_APP_ID = "1:828629511294:web:fae32760ca3c3afcb87c2f";

// Mock window object for Node.js environment
global.window = {};

async function testCollectionFunction() {
  try {
    console.log('Testing Firebase collection() function...');
    
    // Test firebase-config package
    const { getFirebaseDb } = require('firebase-config');
    const { doc, collection } = require('firebase/firestore');
    const { COLLECTIONS } = require('database');
    
    console.log('✅ All packages loaded successfully');
    
    // Test getting database instance
    const db = getFirebaseDb();
    console.log('✅ Firebase database initialized successfully');
    
    // Test collection() function with the database instance
    try {
      const usersCollection = collection(db, COLLECTIONS.USERS);
      console.log('✅ collection() function works with USERS collection');
      
      const driversCollection = collection(db, COLLECTIONS.DRIVERS);
      console.log('✅ collection() function works with DRIVERS collection');
      
      // Test doc() function
      const userDoc = doc(db, COLLECTIONS.USERS, 'test-uid');
      console.log('✅ doc() function works with USERS collection');
      
      console.log('🎉 All Firebase collection operations work correctly!');
      
    } catch (error) {
      console.log('❌ Firebase collection operations failed:', error.message);
      console.log('Error details:', error);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testCollectionFunction();
